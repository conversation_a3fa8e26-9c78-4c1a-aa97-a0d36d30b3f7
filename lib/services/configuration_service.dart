import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:flutter/foundation.dart';

/// Serviço responsável por carregar configurações do aplicativo do banco de dados
///
/// Estrutura esperada no Firebase:
/// Collection: 'settings'
/// Document: 'DriverConfiguration'
/// Campos:
/// - maxStoreDistanceInKM: number (distância máxima em km para mostrar lojas)
///
/// Exemplo de documento no Firebase:
/// {
///   "maxStoreDistanceInKM": 5.0
/// }
class ConfigurationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<void> loadMaxStoreDistance() async {
    try {
      debugPrint(
          'ConfigurationService: Carregando maxStoreDistanceInKM do banco de dados...');

      DocumentSnapshot<Map<String, dynamic>> configDoc = await _firestore
          .collection('driverConfiguration')
          .doc('DriverConfiguration')
          .get();
      if (configDoc.exists) {
        double? distance =
            configDoc.data()?['maxStoreDistanceInKM']?.toDouble();
        if (distance != null) {
          maxStoreDistanceInKM = distance;
          debugPrint(
              'ConfigurationService: maxStoreDistanceInKM carregado com sucesso: $maxStoreDistanceInKM km');
        } else {
          debugPrint(
              'ConfigurationService: maxStoreDistanceInKM é null, usando valor padrão de 5.0 km');
          maxStoreDistanceInKM = 8.0;
        }
      } else {
        debugPrint(
            'ConfigurationService: Documento DriverConfiguration não encontrado, usando valor padrão de 5.0 km');
        maxStoreDistanceInKM = 8.0;
      }
    } catch (e) {
      debugPrint(
          'ConfigurationService: Erro ao carregar maxStoreDistanceInKM: $e');
      maxStoreDistanceInKM = 8.0; // Valor padrão em caso de erro
    }
  }

  /// Obtém o valor atual da distância máxima
  static double getCurrentMaxStoreDistance() {
    return maxStoreDistanceInKM;
  }
}
