name: emartdriver
description: A new Flutter application.
publish_to: "none" # Remove this line if you wish to publish to pub.dev
version: 1.4.0+32

environment:
  sdk: ">=3.0.5 <4.0.0"

dependencies:
  audioplayers: ^6.5.0
  bottom_picker: ^4.1.0
  cached_network_image: ^3.4.1
  cloud_firestore: ^5.6.12
  cloud_functions: ^5.6.2
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8
  dotted_border: ^2.1.0
  easy_localization: ^3.0.7+1
  firebase_app_check: ^0.3.2+10
  firebase_auth: ^5.7.0
  firebase_core: ^3.15.2
  firebase_database: ^11.3.10
  firebase_messaging: ^15.2.10
  firebase_storage: ^12.4.10
  flutter:
    sdk: flutter
  flutter_dash: ^1.0.0
  flutter_easyloading: ^3.0.5
  flutter_email_sender: ^6.0.3

  flutter_html: any
  flutter_launcher_icons: ^0.14.4
  flutter_local_notifications: ^19.3.1
  flutter_localizations:
    sdk: flutter
  flutter_map: ^8.1.1
  # flutter_osm_plugin: ^1.3.3+1  # Removed due to iOS build compatibility issues
  flutter_otp_text_field: ^1.2.0
  flutter_polyline_points: ^3.0.0
  flutter_svg: ^2.2.0
  flutter_widget_from_html: ^0.17.0
  geocoding: ^4.0.0
  geolocator: ^14.0.2
  get: ^4.7.2
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.12.3
  google_maps_flutter_ios: ^2.15.4
  # Temporarily commenting out to fix build issues
  # google_maps_place_picker_mb: ^3.1.2

  http: ^1.5.0-beta.2
  image_picker: ^1.1.2
  intl: ^0.20.2
  intl_phone_number_input: ^0.7.4
  location: ^8.0.1
  mailer: ^6.4.1
  map_launcher: ^3.5.0
  osm_nominatim: ^4.0.1
  package_info_plus: ^8.3.0
  path_provider: ^2.1.4
  photo_view: ^0.15.0
  pin_code_fields: ^8.0.1
  provider: ^6.1.5
  # razorpay_flutter: ^1.3.7
  share_plus: ^11.0.0
  shared_preferences: ^2.5.3
  shorebird_code_push: ^2.0.4
  syncfusion_flutter_datepicker: ^30.2.4
  the_apple_sign_in: ^1.1.1
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  video_compress: ^3.1.4
  video_player: ^2.10.0
  webview_flutter: ^4.10.0
  webview_flutter_android: ^4.3.4
  flutter_lints: ^6.0.0
  mask_text_input_formatter: ^2.9.0
  google_api_availability: ^5.0.1

  rxdart: any
  flutter_foreground_task: ^9.1.0
  map_fields: ^0.0.5
  upgrader: ^11.4.0
  signalr_netcore: ^1.4.3
dependency_overrides:
  js: ^0.7.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_logo.png"

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/talisologo.png
    - assets/translations/
    - assets/fonts/
    - assets/flags/
    - assets/audio/
    - shorebird.yaml

  fonts:
    - family: RadioCanadaBig-Bold
      fonts:
        - asset: assets/fonts/RadioCanadaBig-Bold.ttf
    - family: Metropolis-Medium
      fonts:
        - asset: assets/fonts/RadioCanadaBig-Medium.ttf
    - family: RadioCanadaBig-Regular
      fonts:
        - asset: assets/fonts/RadioCanadaBig-Regular.ttf
    - family: RadioCanadaBig-SemiBold
      fonts:
        - asset: assets/fonts/RadioCanadaBig-SemiBold.ttf
