from datetime import datetime
import firebase_admin
from firebase_admin import credentials, firestore
import json
import sys
import logging

# Configuração de logging
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)


def inicializar_firebase():
    cred = credentials.Certificate(**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)
    try:
        firebase_admin.initialize_app(cred)
        logging.info("Firebase inicializado com sucesso.")
    except Exception as e:
        logging.error(f"Erro ao inicializar Firebase: {e}")
        sys.exit(1)


def serializar_valores(obj):
    if isinstance(obj, dict):
        return {k: serializar_valores(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [serializar_valores(v) for v in obj]
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    return obj


def buscar_pedido(pedido_id: str, collection="vendor_orders"):
    db = firestore.client()
    pedido_ref = db.collection(collection).document(pedido_id)
    pedido = pedido_ref.get()
    if not pedido.exists:
        logging.warning(f"Pedido não encontrado para o ID: {pedido_id}")
        return None
    return pedido.to_dict()


def salvar_json(dado, nome_arquivo):
    try:
        with open(nome_arquivo, "w", encoding="utf-8") as f:
            json.dump(dado, f, ensure_ascii=False, indent=2)
        logging.info(f"Arquivo salvo: {nome_arquivo}")
    except Exception as e:
        logging.error(f"Erro ao salvar arquivo {nome_arquivo}: {e}")

def main(pedido_id):
    inicializar_firebase()
    pedido_dict = buscar_pedido(pedido_id)
    if pedido_dict is not None:
        pedido_serializado = serializar_valores(pedido_dict)
        salvar_json(pedido_serializado, f"{pedido_id}.json")
    else:
        print("Pedido não encontrado para o ID informado.")


if __name__ == "__main__":
    # Se quiser passar o pedido_id por argumento, descomente a linha abaixo
    # pedido_id = sys.argv[1] if len(sys.argv) > 1 else "0610fNIZEUkI3PGDdsmu"

    # pelo termiunal
    pedido_id = input("Digite o ID do pedido: ")
    main(pedido_id)
